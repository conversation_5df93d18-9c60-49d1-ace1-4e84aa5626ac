# app/schemas/entreprise_tiers.py
"""
Schémas Pydantic pour les entreprises tierces
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, validator


class EntrepriseTiersBase(BaseModel):
    """Schéma de base pour les entreprises tierces"""
    nom_entreprise: str = Field(..., min_length=1, max_length=255, description="Nom de l'entreprise")
    activite: Optional[str] = Field(None, max_length=255, description="Secteur d'activité")
    adresse: Optional[str] = Field(None, description="Adresse complète")
    code_postal: Optional[str] = Field(None, max_length=10, description="Code postal")
    ville: Optional[str] = Field(None, max_length=100, description="Ville")
    pays: Optional[str] = Field("France", max_length=100, description="Pays")
    telephone: Optional[str] = Field(None, max_length=20, description="Numéro de téléphone")
    fax: Optional[str] = Field(None, max_length=20, description="Numéro de fax")
    email: Optional[EmailStr] = Field(None, description="Adresse email")
    siret: Optional[str] = Field(None, min_length=14, max_length=14, description="Numéro SIRET")
    tva_intracommunautaire: Optional[str] = Field(None, max_length=20, description="Numéro TVA intracommunautaire")
    representant_legal_id: Optional[int] = Field(None, description="ID du représentant légal")

    @validator('siret')
    def validate_siret(cls, v):
        if v is not None:
            # Supprimer les espaces et vérifier que c'est bien 14 chiffres
            v = v.replace(' ', '')
            if not v.isdigit() or len(v) != 14:
                raise ValueError('Le SIRET doit contenir exactement 14 chiffres')
        return v

    @validator('telephone', 'fax')
    def validate_phone(cls, v):
        if v is not None:
            # Nettoyer le numéro de téléphone
            v = v.replace(' ', '').replace('.', '').replace('-', '')
            if not v.replace('+', '').isdigit():
                raise ValueError('Le numéro doit contenir uniquement des chiffres, espaces, points, tirets ou +')
        return v


class EntrepriseTiersCreate(EntrepriseTiersBase):
    """Schéma pour la création d'une entreprise tierce"""
    pass


class EntrepriseTiersUpdate(BaseModel):
    """Schéma pour la mise à jour d'une entreprise tierce"""
    nom_entreprise: Optional[str] = Field(None, min_length=1, max_length=255)
    activite: Optional[str] = Field(None, max_length=255)
    adresse: Optional[str] = None
    code_postal: Optional[str] = Field(None, max_length=10)
    ville: Optional[str] = Field(None, max_length=100)
    pays: Optional[str] = Field(None, max_length=100)
    telephone: Optional[str] = Field(None, max_length=20)
    fax: Optional[str] = Field(None, max_length=20)
    email: Optional[EmailStr] = None
    siret: Optional[str] = Field(None, min_length=14, max_length=14)
    tva_intracommunautaire: Optional[str] = Field(None, max_length=20)
    representant_legal_id: Optional[int] = None
    is_active: Optional[bool] = None

    @validator('siret')
    def validate_siret(cls, v):
        if v is not None:
            v = v.replace(' ', '')
            if not v.isdigit() or len(v) != 14:
                raise ValueError('Le SIRET doit contenir exactement 14 chiffres')
        return v


class EntrepriseTiersResponse(EntrepriseTiersBase):
    """Schéma pour la réponse d'une entreprise tierce"""
    id: int
    company_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[int]

    # Informations du représentant légal (si présent)
    representant_legal_nom: Optional[str] = None
    representant_legal_email: Optional[str] = None

    class Config:
        from_attributes = True


class EntrepriseTiersList(BaseModel):
    """Schéma pour la liste paginée des entreprises tierces"""
    items: list[EntrepriseTiersResponse]
    total: int
    page: int
    size: int
    pages: int

    class Config:
        from_attributes = True


class EntrepriseTiersStats(BaseModel):
    """Schéma pour les statistiques des entreprises tierces"""
    total_entreprises: int
    entreprises_actives: int
    entreprises_inactives: int
    entreprises_avec_siret: int
    entreprises_avec_email: int
    activites_principales: list[dict[str, int]]

    class Config:
        from_attributes = True
