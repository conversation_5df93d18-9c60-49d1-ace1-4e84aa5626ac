# app/models/company.py
from sqlalchemy import <PERSON>olean, Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class CompanyRole(str, enum.Enum):
    """
    Énumération complète des rôles d'entreprise
    Inclut les rôles de gestion ET les rôles métier BTP
    """
    # Rôles de gestion
    ADMIN = "ADMIN"          # Administrateur d'entreprise
    MANAGER = "MANAGER"      # Gestionnaire
    USER = "USER"           # Utilisateur standard
    VIEWER = "VIEWER"       # Lecture seule

    # Rôles métier BTP
    MOA = "MOA"             # Maître d'Ouvrage
    MOADEL = "MOADEL"       # Maître d'Ouvrage Délégué
    ARCHI = "ARCHI"         # Architecte
    BE = "BE"               # Bureau d'Études
    BC = "BC"               # Bureau de Contrôle
    OPC = "OPC"             # Ordonnancement Pilotage Coordination
    ENT = "ENT"             # Entreprise
    FO = "FO"               # Fournisseur

class Company(Base):
    __tablename__ = "companies"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)
    code = Column(String, unique=True, nullable=False, index=True)
    description = Column(Text)
    address = Column(Text)
    phone = Column(String)
    email = Column(String)
    website = Column(String)
    siret = Column(String)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    users = relationship("UserCompany", back_populates="company")
    settings = relationship("CompanySettings", back_populates="company", uselist=False)
    project_associations = relationship("ProjectCompany", back_populates="company")
    employees = relationship("Employee", back_populates="company")
    suppliers = relationship("Supplier", back_populates="company")
    materials = relationship("Material", back_populates="company")
    budgets = relationship("Budget", back_populates="company")
    purchase_orders = relationship("PurchaseOrder", back_populates="company")
    quotes = relationship("Quote", back_populates="company")
    documents = relationship("Document", back_populates="company")

    # Nouvelles relations RBAC
    role_permissions = relationship("CompanyRolePermission", back_populates="company", cascade="all, delete-orphan")

    # Relation avec les entreprises tierces
    entreprises_tiers = relationship("EntrepriseTiers", back_populates="company", cascade="all, delete-orphan")

    @property
    def projects(self):
        """Retourne les projets associés à cette entreprise"""
        return [pc.project for pc in self.project_associations if pc.is_active]

class UserCompany(Base):
    __tablename__ = "user_companies"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    role_name = Column(String(50), nullable=False, default="USER")  # Nom du rôle (plus d'enum)
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    invited_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    invited_at = Column(DateTime, nullable=True)
    joined_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = relationship("User", back_populates="companies", foreign_keys=[user_id])
    company = relationship("Company", back_populates="users")
    inviter = relationship("User", foreign_keys=[invited_by])

    @property
    def role(self):
        """Propriété de compatibilité pour l'ancien système"""
        try:
            return CompanyRole(self.role_name)
        except ValueError:
            return CompanyRole.USER

    def has_permission(self, permission_name: str) -> bool:
        """
        Vérifie si l'utilisateur a une permission spécifique dans cette entreprise
        """
        from sqlalchemy.orm import Session
        from app.models.rbac import CompanyRolePermission, Permission

        # Pour les tests, on peut utiliser une session depuis l'objet
        if hasattr(self, '_sa_instance_state') and self._sa_instance_state.session:
            session = self._sa_instance_state.session

            # Rechercher la permission
            permission = session.query(Permission).filter(
                Permission.name == permission_name
            ).first()

            if not permission:
                return False

            # Vérifier si le rôle a cette permission dans cette entreprise
            has_perm = session.query(CompanyRolePermission).filter(
                CompanyRolePermission.company_id == self.company_id,
                CompanyRolePermission.role_name == self.role_name,
                CompanyRolePermission.permission_id == permission.id
            ).first()

            return has_perm is not None

        return False

    def get_permissions(self) -> list:
        """
        Récupère toutes les permissions de l'utilisateur dans cette entreprise
        """
        from sqlalchemy.orm import Session
        from app.models.rbac import CompanyRolePermission, Permission

        if hasattr(self, '_sa_instance_state') and self._sa_instance_state.session:
            session = self._sa_instance_state.session

            permissions = session.query(Permission).join(
                CompanyRolePermission,
                CompanyRolePermission.permission_id == Permission.id
            ).filter(
                CompanyRolePermission.company_id == self.company_id,
                CompanyRolePermission.role_name == self.role_name
            ).all()

            return [perm.name for perm in permissions]

        return []

class CompanySettings(Base):
    __tablename__ = "company_settings"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    default_currency = Column(String, default="EUR")
    date_format = Column(String, default="DD/MM/YYYY")
    time_format = Column(String, default="24h")
    language = Column(String, default="fr")
    logo_url = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    company = relationship("Company", back_populates="settings")