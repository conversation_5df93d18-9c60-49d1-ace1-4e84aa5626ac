# app/crud/entreprise_tiers.py
"""
CRUD operations pour les entreprises tierces
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from app.models.entreprise_tiers import EntrepriseTiers
from app.models.user import User
from app.schemas.entreprise_tiers import EntrepriseTiersCreate, EntrepriseTiersUpdate


class EntrepriseTiersCRUD:
    """Service CRUD pour les entreprises tierces"""

    @staticmethod
    def get_by_id(db: Session, entreprise_id: int, company_id: int) -> Optional[EntrepriseTiers]:
        """Récupérer une entreprise tierce par son ID"""
        return db.query(EntrepriseTiers).options(
            joinedload(EntrepriseTiers.representant_legal),
            joinedload(EntrepriseTiers.created_by_user)
        ).filter(
            and_(
                EntrepriseTiers.id == entreprise_id,
                EntrepriseTiers.company_id == company_id
            )
        ).first()

    @staticmethod
    def get_by_siret(db: Session, siret: str, company_id: int) -> Optional[EntrepriseTiers]:
        """Récupérer une entreprise tierce par son SIRET"""
        return db.query(EntrepriseTiers).filter(
            and_(
                EntrepriseTiers.siret == siret,
                EntrepriseTiers.company_id == company_id
            )
        ).first()

    @staticmethod
    def get_multi(
        db: Session,
        company_id: int,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        is_active: Optional[bool] = None,
        activite: Optional[str] = None
    ) -> tuple[List[EntrepriseTiers], int]:
        """Récupérer une liste paginée d'entreprises tierces avec filtres"""
        
        query = db.query(EntrepriseTiers).options(
            joinedload(EntrepriseTiers.representant_legal)
        ).filter(EntrepriseTiers.company_id == company_id)

        # Filtres
        if is_active is not None:
            query = query.filter(EntrepriseTiers.is_active == is_active)
        
        if activite:
            query = query.filter(EntrepriseTiers.activite.ilike(f"%{activite}%"))
        
        if search:
            search_filter = or_(
                EntrepriseTiers.nom_entreprise.ilike(f"%{search}%"),
                EntrepriseTiers.email.ilike(f"%{search}%"),
                EntrepriseTiers.siret.ilike(f"%{search}%"),
                EntrepriseTiers.ville.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)

        # Compter le total
        total = query.count()

        # Pagination et tri
        entreprises = query.order_by(desc(EntrepriseTiers.created_at)).offset(skip).limit(limit).all()

        return entreprises, total

    @staticmethod
    def create(db: Session, entreprise_data: EntrepriseTiersCreate, company_id: int, created_by: int) -> EntrepriseTiers:
        """Créer une nouvelle entreprise tierce"""
        
        # Vérifier l'unicité du SIRET si fourni
        if entreprise_data.siret:
            existing = EntrepriseTiersCRUD.get_by_siret(db, entreprise_data.siret, company_id)
            if existing:
                raise ValueError(f"Une entreprise avec le SIRET {entreprise_data.siret} existe déjà")

        db_entreprise = EntrepriseTiers(
            **entreprise_data.dict(),
            company_id=company_id,
            created_by=created_by
        )
        
        db.add(db_entreprise)
        db.commit()
        db.refresh(db_entreprise)
        
        return db_entreprise

    @staticmethod
    def update(
        db: Session,
        entreprise_id: int,
        company_id: int,
        entreprise_data: EntrepriseTiersUpdate
    ) -> Optional[EntrepriseTiers]:
        """Mettre à jour une entreprise tierce"""
        
        db_entreprise = EntrepriseTiersCRUD.get_by_id(db, entreprise_id, company_id)
        if not db_entreprise:
            return None

        # Vérifier l'unicité du SIRET si modifié
        if entreprise_data.siret and entreprise_data.siret != db_entreprise.siret:
            existing = EntrepriseTiersCRUD.get_by_siret(db, entreprise_data.siret, company_id)
            if existing and existing.id != entreprise_id:
                raise ValueError(f"Une entreprise avec le SIRET {entreprise_data.siret} existe déjà")

        # Mettre à jour les champs modifiés
        update_data = entreprise_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_entreprise, field, value)

        db.commit()
        db.refresh(db_entreprise)
        
        return db_entreprise

    @staticmethod
    def delete(db: Session, entreprise_id: int, company_id: int) -> bool:
        """Supprimer une entreprise tierce (soft delete)"""
        
        db_entreprise = EntrepriseTiersCRUD.get_by_id(db, entreprise_id, company_id)
        if not db_entreprise:
            return False

        db_entreprise.is_active = False
        db.commit()
        
        return True

    @staticmethod
    def get_stats(db: Session, company_id: int) -> Dict[str, Any]:
        """Récupérer les statistiques des entreprises tierces"""
        
        # Statistiques de base
        total = db.query(EntrepriseTiers).filter(EntrepriseTiers.company_id == company_id).count()
        actives = db.query(EntrepriseTiers).filter(
            and_(EntrepriseTiers.company_id == company_id, EntrepriseTiers.is_active == True)
        ).count()
        avec_siret = db.query(EntrepriseTiers).filter(
            and_(
                EntrepriseTiers.company_id == company_id,
                EntrepriseTiers.siret.isnot(None),
                EntrepriseTiers.siret != ""
            )
        ).count()
        avec_email = db.query(EntrepriseTiers).filter(
            and_(
                EntrepriseTiers.company_id == company_id,
                EntrepriseTiers.email.isnot(None),
                EntrepriseTiers.email != ""
            )
        ).count()

        # Top activités
        activites = db.query(
            EntrepriseTiers.activite,
            func.count(EntrepriseTiers.id).label('count')
        ).filter(
            and_(
                EntrepriseTiers.company_id == company_id,
                EntrepriseTiers.activite.isnot(None),
                EntrepriseTiers.activite != ""
            )
        ).group_by(EntrepriseTiers.activite).order_by(desc('count')).limit(10).all()

        return {
            "total_entreprises": total,
            "entreprises_actives": actives,
            "entreprises_inactives": total - actives,
            "entreprises_avec_siret": avec_siret,
            "entreprises_avec_email": avec_email,
            "activites_principales": [{"activite": a.activite, "count": a.count} for a in activites]
        }
