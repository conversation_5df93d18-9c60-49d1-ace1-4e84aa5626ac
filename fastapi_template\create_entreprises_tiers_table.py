#!/usr/bin/env python3
"""
Script pour créer la table entreprises_tiers
"""

import asyncio
import asyncpg
from app.core.config import settings

async def create_entreprises_tiers_table():
    """Créer la table entreprises_tiers"""
    
    # Connexion à la base de données
    conn = await asyncpg.connect(settings.DATABASE_URL)
    
    try:
        # Vérifier si la table existe déjà
        table_exists = await conn.fetchval("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'entreprises_tiers'
            );
        """)
        
        if table_exists:
            print("✅ La table 'entreprises_tiers' existe déjà")
            return
        
        # Créer la table entreprises_tiers
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS public.entreprises_tiers (
            id SERIAL PRIMARY KEY,
            nom_entreprise VARCHAR(255) NOT NULL,
            activite VARCHAR(255),
            adresse T<PERSON>,
            code_postal VARCHAR(10),
            ville VARCHAR(100),
            pays VARCHAR(100) DEFAULT 'France',
            telephone VARCHAR(20),
            fax VARCHAR(20),
            email VARCHAR(255),
            siret VARCHAR(14),
            tva_intracommunautaire VARCHAR(20),
            representant_legal_id INTEGER REFERENCES public.users(id),
            company_id INTEGER NOT NULL REFERENCES public.companies(id),
            is_active BOOLEAN NOT NULL DEFAULT true,
            created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            created_by INTEGER REFERENCES public.users(id)
        );
        """
        
        await conn.execute(create_table_sql)
        print("✅ Table 'entreprises_tiers' créée avec succès")
        
        # Créer les index
        indexes = [
            "CREATE INDEX IF NOT EXISTS ix_entreprises_tiers_id ON entreprises_tiers(id);",
            "CREATE INDEX IF NOT EXISTS ix_entreprises_tiers_nom_entreprise ON entreprises_tiers(nom_entreprise);",
            "CREATE INDEX IF NOT EXISTS ix_entreprises_tiers_email ON entreprises_tiers(email);",
            "CREATE UNIQUE INDEX IF NOT EXISTS ix_entreprises_tiers_siret ON entreprises_tiers(siret) WHERE siret IS NOT NULL;",
            "CREATE INDEX IF NOT EXISTS ix_entreprises_tiers_company_id ON entreprises_tiers(company_id);"
        ]
        
        for index_sql in indexes:
            await conn.execute(index_sql)
        
        print("✅ Index créés avec succès")
        
        # Créer un trigger pour updated_at
        trigger_sql = """
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';

        DROP TRIGGER IF EXISTS update_entreprises_tiers_updated_at ON entreprises_tiers;
        CREATE TRIGGER update_entreprises_tiers_updated_at
            BEFORE UPDATE ON entreprises_tiers
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        
        await conn.execute(trigger_sql)
        print("✅ Trigger pour updated_at créé avec succès")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de la table: {e}")
        raise
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(create_entreprises_tiers_table())
