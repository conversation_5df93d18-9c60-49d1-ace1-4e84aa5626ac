# app/api/api_v1/endpoints/entreprises_tiers.py
"""
API endpoints pour les entreprises tierces (carnet d'adresses)
"""

from typing import List, Optional, Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func

from app.api import deps
from app.models.user import User
from app.models.company import UserCompany, Company
from app.models.entreprise_tiers import EntrepriseTiers
from app.schemas.entreprise_tiers import (
    EntrepriseTiersCreate,
    EntrepriseTiersUpdate,
    EntrepriseTiersResponse,
    EntrepriseTiersList,
    EntrepriseTiersStats
)
from app.core.jwt_auth import require_auth

router = APIRouter()


@router.get("/", response_model=List[EntrepriseTiersResponse])
async def get_entreprises_tiers(
    db: AsyncSession = Depends(deps.get_db),
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(100, ge=1, le=1000, description="Nombre maximum d'éléments à retourner"),
    search: Optional[str] = Query(None, description="Recherche dans nom, email, SIRET, ville"),
    is_active: Optional[bool] = Query(None, description="Filtrer par statut actif/inactif"),
    activite: Optional[str] = Query(None, description="Filtrer par activité"),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer la liste des entreprises tierces avec pagination et filtres"""
    try:
        print(f"🔍 GET /entreprises-tiers - User: {current_user}")

        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'entreprise de l'utilisateur via UserCompany
        user_company_result = await db.execute(
            select(UserCompany).where(
                and_(
                    UserCompany.user_id == user_id,
                    UserCompany.is_active == True
                )
            ).limit(1)
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à une entreprise")

        # Construire la requête de base
        query = select(EntrepriseTiers).where(
            and_(
                EntrepriseTiers.company_id == user_company.company_id,
                EntrepriseTiers.is_active == True if is_active is None else EntrepriseTiers.is_active == is_active
            )
        )

        # Ajouter les filtres
        if search:
            search_filter = or_(
                EntrepriseTiers.nom_entreprise.ilike(f"%{search}%"),
                EntrepriseTiers.email.ilike(f"%{search}%"),
                EntrepriseTiers.siret.ilike(f"%{search}%"),
                EntrepriseTiers.ville.ilike(f"%{search}%")
            )
            query = query.where(search_filter)

        if activite:
            query = query.where(EntrepriseTiers.activite.ilike(f"%{activite}%"))

        # Ajouter pagination
        query = query.offset(skip).limit(limit)

        # Exécuter la requête
        result = await db.execute(query)
        entreprises = result.scalars().all()

        print(f"✅ Found {len(entreprises)} entreprises tierces")
        return entreprises

    except Exception as e:
        print(f"❌ Error in get_entreprises_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des entreprises tierces: {str(e)}")


@router.get("/{entreprise_id}", response_model=EntrepriseTiersResponse)
async def get_entreprise_tiers(
    entreprise_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Récupérer une entreprise tierce par son ID"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'entreprise de l'utilisateur via UserCompany
        user_company_result = await db.execute(
            select(UserCompany).where(
                and_(
                    UserCompany.user_id == user_id,
                    UserCompany.is_active == True
                )
            ).limit(1)
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à une entreprise")

        # Récupérer l'entreprise tierce
        result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == entreprise_id,
                    EntrepriseTiers.company_id == user_company.company_id,
                    EntrepriseTiers.is_active == True
                )
            )
        )
        entreprise = result.scalar_one_or_none()

        if not entreprise:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entreprise tierce non trouvée"
            )

        return entreprise

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in get_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération de l'entreprise tierce: {str(e)}")


@router.post("/", response_model=EntrepriseTiersResponse, status_code=status.HTTP_201_CREATED)
async def create_entreprise_tiers(
    *,
    db: AsyncSession = Depends(deps.get_db),
    entreprise_in: EntrepriseTiersCreate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Créer une nouvelle entreprise tierce"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'entreprise de l'utilisateur via UserCompany
        user_company_result = await db.execute(
            select(UserCompany).where(
                and_(
                    UserCompany.user_id == user_id,
                    UserCompany.is_active == True
                )
            ).limit(1)
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à une entreprise")

        # Vérifier l'unicité du SIRET si fourni
        if entreprise_in.siret:
            existing_result = await db.execute(
                select(EntrepriseTiers).where(
                    and_(
                        EntrepriseTiers.siret == entreprise_in.siret,
                        EntrepriseTiers.company_id == user_company.company_id,
                        EntrepriseTiers.is_active == True
                    )
                )
            )
            existing = existing_result.scalar_one_or_none()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Une entreprise avec le SIRET {entreprise_in.siret} existe déjà"
                )

        # Créer l'entreprise tierce
        entreprise_data = entreprise_in.dict(exclude_unset=True)
        entreprise_data["company_id"] = user_company.company_id
        entreprise_data["created_by"] = user_id

        db_entreprise = EntrepriseTiers(**entreprise_data)
        db.add(db_entreprise)
        await db.commit()
        await db.refresh(db_entreprise)

        print(f"✅ Created entreprise tierce: {db_entreprise.nom_entreprise}")
        return db_entreprise

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in create_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la création de l'entreprise tierce: {str(e)}")


@router.put("/{entreprise_id}", response_model=EntrepriseTiersResponse)
async def update_entreprise_tiers(
    *,
    entreprise_id: int,
    db: AsyncSession = Depends(deps.get_db),
    entreprise_in: EntrepriseTiersUpdate,
    current_user: Dict[str, Any] = Depends(require_auth),
) -> Any:
    """Mettre à jour une entreprise tierce"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'entreprise de l'utilisateur via UserCompany
        user_company_result = await db.execute(
            select(UserCompany).where(
                and_(
                    UserCompany.user_id == user_id,
                    UserCompany.is_active == True
                )
            ).limit(1)
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à une entreprise")

        # Récupérer l'entreprise tierce existante
        result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == entreprise_id,
                    EntrepriseTiers.company_id == user_company.company_id,
                    EntrepriseTiers.is_active == True
                )
            )
        )
        db_entreprise = result.scalar_one_or_none()

        if not db_entreprise:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entreprise tierce non trouvée"
            )

        # Vérifier l'unicité du SIRET si modifié
        if entreprise_in.siret and entreprise_in.siret != db_entreprise.siret:
            existing_result = await db.execute(
                select(EntrepriseTiers).where(
                    and_(
                        EntrepriseTiers.siret == entreprise_in.siret,
                        EntrepriseTiers.company_id == user_company.company_id,
                        EntrepriseTiers.id != entreprise_id,
                        EntrepriseTiers.is_active == True
                    )
                )
            )
            existing = existing_result.scalar_one_or_none()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Une entreprise avec le SIRET {entreprise_in.siret} existe déjà"
                )

        # Mettre à jour les champs modifiés
        update_data = entreprise_in.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_entreprise, field, value)

        await db.commit()
        await db.refresh(db_entreprise)

        print(f"✅ Updated entreprise tierce: {db_entreprise.nom_entreprise}")
        return db_entreprise

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in update_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la mise à jour de l'entreprise tierce: {str(e)}")


@router.delete("/{entreprise_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_entreprise_tiers(
    *,
    entreprise_id: int,
    db: AsyncSession = Depends(deps.get_db),
    current_user: Dict[str, Any] = Depends(require_auth),
):
    """Supprimer une entreprise tierce (soft delete)"""
    try:
        user_id = current_user.get("user_id")
        if not user_id:
            raise HTTPException(status_code=400, detail="Token utilisateur invalide")

        # Récupérer l'entreprise de l'utilisateur via UserCompany
        user_company_result = await db.execute(
            select(UserCompany).where(
                and_(
                    UserCompany.user_id == user_id,
                    UserCompany.is_active == True
                )
            ).limit(1)
        )
        user_company = user_company_result.scalar_one_or_none()
        if not user_company:
            raise HTTPException(status_code=403, detail="Utilisateur non associé à une entreprise")

        # Récupérer l'entreprise tierce existante
        result = await db.execute(
            select(EntrepriseTiers).where(
                and_(
                    EntrepriseTiers.id == entreprise_id,
                    EntrepriseTiers.company_id == user_company.company_id,
                    EntrepriseTiers.is_active == True
                )
            )
        )
        db_entreprise = result.scalar_one_or_none()

        if not db_entreprise:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entreprise tierce non trouvée"
            )

        # Soft delete
        db_entreprise.is_active = False
        await db.commit()

        print(f"✅ Deleted entreprise tierce: {db_entreprise.nom_entreprise}")

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error in delete_entreprise_tiers: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Erreur lors de la suppression de l'entreprise tierce: {str(e)}")
