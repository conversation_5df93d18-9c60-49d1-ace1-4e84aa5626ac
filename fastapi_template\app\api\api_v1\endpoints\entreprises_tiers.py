# app/api/api_v1/endpoints/entreprises_tiers.py
"""
API endpoints pour les entreprises tierces (carnet d'adresses)
"""

from typing import List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.company import UserCompany
from app.schemas.entreprise_tiers import (
    EntrepriseTiersCreate,
    EntrepriseTiersUpdate,
    EntrepriseTiersResponse,
    EntrepriseTiersList,
    EntrepriseTiersStats
)
from app.crud.entreprise_tiers import EntrepriseTiersCRUD

router = APIRouter()


def get_user_company_id(current_user: User, db: Session) -> int:
    """Récupérer l'ID de l'entreprise de l'utilisateur connecté"""
    if current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Les super administrateurs ne peuvent pas accéder aux entreprises tierces"
        )

    user_company = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).first()
    if not user_company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Utilisateur non associé à une entreprise"
        )

    return user_company.company_id


@router.get("/", response_model=EntrepriseTiersList)
def get_entreprises_tiers(
    skip: int = Query(0, ge=0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(50, ge=1, le=100, description="Nombre d'éléments à retourner"),
    search: Optional[str] = Query(None, description="Recherche dans nom, email, SIRET, ville"),
    is_active: Optional[bool] = Query(None, description="Filtrer par statut actif/inactif"),
    activite: Optional[str] = Query(None, description="Filtrer par activité"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """Récupérer la liste des entreprises tierces avec pagination et filtres"""

    # Récupérer l'ID de l'entreprise de l'utilisateur
    company_id = get_user_company_id(current_user, db)

    # Pour l'instant, retournons une liste vide en attendant la migration
    # TODO: Implémenter après la migration de la base de données
    return EntrepriseTiersList(
        items=[],
        total=0,
        page=1,
        size=limit,
        pages=0
    )


@router.get("/stats", response_model=EntrepriseTiersStats)
def get_entreprises_tiers_stats(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """Récupérer les statistiques des entreprises tierces"""

    # Récupérer l'ID de l'entreprise de l'utilisateur
    company_id = get_user_company_id(current_user, db)

    # Pour l'instant, retournons des statistiques vides
    return EntrepriseTiersStats(
        total_entreprises=0,
        entreprises_actives=0,
        entreprises_inactives=0,
        entreprises_avec_siret=0,
        entreprises_avec_email=0,
        activites_principales=[]
    )


@router.get("/{entreprise_id}", response_model=EntrepriseTiersResponse)
def get_entreprise_tiers(
    entreprise_id: int,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> Any:
    """Récupérer une entreprise tierce par son ID"""

    # Récupérer l'ID de l'entreprise de l'utilisateur
    company_id = get_user_company_id(current_user, db)

    # Pour l'instant, retournons une erreur 404
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="Entreprise tierce non trouvée"
    )


@router.post("/", response_model=EntrepriseTiersResponse, status_code=status.HTTP_201_CREATED)
def create_entreprise_tiers(
    entreprise_data: EntrepriseTiersCreate,
    company_id: int = Depends(get_user_company_id),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Créer une nouvelle entreprise tierce"""
    
    try:
        entreprise = EntrepriseTiersCRUD.create(
            db=db,
            entreprise_data=entreprise_data,
            company_id=company_id,
            created_by=current_user.id
        )
        
        response = EntrepriseTiersResponse.from_orm(entreprise)
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{entreprise_id}", response_model=EntrepriseTiersResponse)
def update_entreprise_tiers(
    entreprise_id: int,
    entreprise_data: EntrepriseTiersUpdate,
    company_id: int = Depends(get_user_company_id),
    db: Session = Depends(get_db)
):
    """Mettre à jour une entreprise tierce"""
    
    try:
        entreprise = EntrepriseTiersCRUD.update(
            db=db,
            entreprise_id=entreprise_id,
            company_id=company_id,
            entreprise_data=entreprise_data
        )
        
        if not entreprise:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Entreprise tierce non trouvée"
            )
        
        response = EntrepriseTiersResponse.from_orm(entreprise)
        if entreprise.representant_legal:
            response.representant_legal_nom = f"{entreprise.representant_legal.first_name} {entreprise.representant_legal.last_name}"
            response.representant_legal_email = entreprise.representant_legal.email
        
        return response
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{entreprise_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_entreprise_tiers(
    entreprise_id: int,
    company_id: int = Depends(get_user_company_id),
    db: Session = Depends(get_db)
):
    """Supprimer une entreprise tierce (soft delete)"""
    
    success = EntrepriseTiersCRUD.delete(db=db, entreprise_id=entreprise_id, company_id=company_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Entreprise tierce non trouvée"
        )
    
    return None
