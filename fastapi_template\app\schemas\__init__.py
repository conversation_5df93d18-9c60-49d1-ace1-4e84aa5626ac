# app/schemas/__init__.py
from app.schemas.user import User, UserCreate, UserUpdate, UserInDB, UserWithCompanies, UserPermissionSummary
from app.schemas.company import (
    Company, CompanyCreate, CompanyUpdate, UserCompany, CompanySettings,
    CompanyRoleAssignment, CompanyWithUsers, CompanyRolePermissions
)
from app.schemas.rbac import (
    Role, RoleCreate, RoleUpdate, Permission, PermissionCreate, PermissionUpdate,
    CompanyRolePermission, RolePermissionsConfig, UserPermissions, PermissionCheck
)
from app.schemas.project import Project, ProjectCreate, ProjectUpdate, ProjectDocument, ProjectEmployee
from app.schemas.employee import Employee, EmployeeCreate, EmployeeUpdate, TimeEntry, TimeEntryCreate, EmployeeAssignment
from app.schemas.supplier import Supplier, SupplierCreate, SupplierUpdate, SupplierContact
from app.schemas.material import Material, MaterialCreate, MaterialUpdate, MaterialCategory, TechnicalSheet, PriceHistory
from app.schemas.financial import Budget, BudgetCreate, BudgetUpdate, BudgetLine, Invoice, InvoiceCreate, Payment, FinancialReport
from app.schemas.purchase_order import PurchaseOrder, PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderLine, Delivery, DeliveryLine
from app.schemas.quote import Quote, QuoteCreate, QuoteUpdate, QuoteLine, QuoteTemplate
from app.schemas.document import Document, DocumentCreate, DocumentUpdate, DocumentVersion, DocumentFolder

__all__ = [
    # User schemas
    "User", "UserCreate", "UserUpdate", "UserInDB", "UserWithCompanies", "UserPermissionSummary",

    # Company schemas
    "Company", "CompanyCreate", "CompanyUpdate", "UserCompany", "CompanySettings",
    "CompanyRoleAssignment", "CompanyWithUsers", "CompanyRolePermissions",

    # RBAC schemas
    "Role", "RoleCreate", "RoleUpdate", "Permission", "PermissionCreate", "PermissionUpdate",
    "CompanyRolePermission", "RolePermissionsConfig", "UserPermissions", "PermissionCheck",

    # Other schemas
    "Project", "ProjectCreate", "ProjectUpdate", "ProjectDocument", "ProjectEmployee",
    "Employee", "EmployeeCreate", "EmployeeUpdate", "TimeEntry", "TimeEntryCreate", "EmployeeAssignment",
    "Supplier", "SupplierCreate", "SupplierUpdate", "SupplierContact",
    "Material", "MaterialCreate", "MaterialUpdate", "MaterialCategory", "TechnicalSheet", "PriceHistory",
    "Budget", "BudgetCreate", "BudgetUpdate", "BudgetLine", "Invoice", "InvoiceCreate", "Payment", "FinancialReport",
    "PurchaseOrder", "PurchaseOrderCreate", "PurchaseOrderUpdate", "PurchaseOrderLine", "Delivery", "DeliveryLine",
    "Quote", "QuoteCreate", "QuoteUpdate", "QuoteLine", "QuoteTemplate",
    "Document", "DocumentCreate", "DocumentUpdate", "DocumentVersion", "DocumentFolder"
]